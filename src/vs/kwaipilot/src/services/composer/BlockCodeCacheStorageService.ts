import { ServiceModule } from "..";
import { ContextManager } from "../../base/context-manager";
import { SqlLite } from "../sql-lite";
import { MemoryCachedKwaipilotKV } from "../sql-lite/kwaipilotKVCache";
import { LoggerManager } from "../../base/logger";
import { OutPutBlockCodePath, Position } from "shared/lib/misc/blockcode";
import crypto from "crypto";

/**
 * 缓存项的数据结构
 */
export interface BlockCodeCacheItem {
  content: string;
  sessionId: string;
  pos?: Position;
  result: OutPutBlockCodePath | undefined;
  timestamp: number;
  workspaceUri?: string;
  cacheKey?: string; // 添加缓存键字段
}

/**
 * 缓存键的参数
 */
export interface BlockCodeCacheKey {
  content: string;
  sessionId: string;
  pos?: Position;
  workspaceUri?: string;
}

/**
 * BlockCode 结果缓存存储服务
 * 用于持久化存储 getFilePathOfBlockCode 函数的结果，提高性能
 */
export class BlockCodeCacheStorageService extends ServiceModule {
  kwaipilotKV: MemoryCachedKwaipilotKV;

  // 缓存过期时间：7天
  private readonly CACHE_TTL = 7 * 24 * 60 * 60 * 1e3;

  constructor(ext: ContextManager) {
    super(ext);
    const sqlLiteService = this.getService(SqlLite);
    this.kwaipilotKV = new MemoryCachedKwaipilotKV(sqlLiteService, this.getBase(LoggerManager));
  }

  /**
   * 生成缓存键
   * @param params 缓存键参数
   * @returns 缓存键字符串
   */
  private generateCacheKey(params: BlockCodeCacheKey): string {
    const { sessionId, pos, workspaceUri, content } = params;

    // 创建一个包含所有参数的字符串
    const keyData = {
      workspaceUri,
      sessionId,
      pos: pos
        ? {
            start: pos.start.offset,
            end: pos.end.offset,
          }
        : {
            start: 0,
            end: content.length,
          },
    };

    // 使用 JSON.stringify 确保一致性，然后生成 hash
    const keyString = JSON.stringify(keyData);
    const hash = crypto.createHash("md5").update(keyString).digest("hex");

    return `blockCodeCache:${hash}`;
  }

  /**
   * 获取缓存的结果
   * @param params 缓存键参数
   * @returns 缓存的结果，如果不存在或已过期则返回 undefined
   */
  async getCachedResult(params: BlockCodeCacheKey): Promise<OutPutBlockCodePath | undefined> {
    try {
      const cacheKey = this.generateCacheKey(params);
      const cachedItem = await this.kwaipilotKV.getObject<BlockCodeCacheItem>(cacheKey);

      if (!cachedItem) {
        return undefined;
      }

      // 检查是否过期
      const now = Date.now();
      if (now - cachedItem.timestamp > this.CACHE_TTL) {
        // 过期了，删除缓存
        await this.kwaipilotKV.delete(cacheKey);
        return undefined;
      }

      // 验证缓存项的有效性（确保参数匹配）
      if (cachedItem.content !== params.content.trim()
        || cachedItem.sessionId !== params.sessionId
        || cachedItem.workspaceUri !== params.workspaceUri) {
        return undefined;
      }

      return cachedItem.result;
    }
    catch (error) {
      console.error("Error getting cached block code result:", error);
      return undefined;
    }
  }

  /**
   * 缓存结果
   * @param params 缓存键参数
   * @param result 要缓存的结果
   */
  async setCachedResult(params: BlockCodeCacheKey, result: OutPutBlockCodePath | undefined): Promise<void> {
    try {
      const cacheKey = this.generateCacheKey(params);
      const cacheItem: BlockCodeCacheItem = {
        content: params.content.trim(),
        sessionId: params.sessionId,
        pos: params.pos,
        result,
        timestamp: Date.now(),
        workspaceUri: params.workspaceUri,
        cacheKey: cacheKey, // 保存缓存键
      };

      await this.kwaipilotKV.set(cacheKey, cacheItem);

      // 维护 sessionId 到缓存键的映射
      await this.addCacheKeyToSessionIndex(params.sessionId, cacheKey);
    }
    catch (error) {
      console.error("Error setting cached block code result:", error);
    }
  }

  /**
   * 添加缓存键到会话索引
   * @param sessionId 会话ID
   * @param cacheKey 缓存键
   */
  private async addCacheKeyToSessionIndex(sessionId: string, cacheKey: string): Promise<void> {
    try {
      const indexKey = this.getSessionIndexKey(sessionId);
      const existingKeys = await this.kwaipilotKV.getObject<string[]>(indexKey, []);

      if (!existingKeys.includes(cacheKey)) {
        existingKeys.push(cacheKey);
        await this.kwaipilotKV.set(indexKey, existingKeys);
      }

      // 同时维护全局会话索引
      await this.addToGlobalSessionIndex(sessionId);
    }
    catch (error) {
      console.error("Error adding cache key to session index:", error);
    }
  }

  /**
   * 添加会话到全局索引
   * @param sessionId 会话ID
   */
  private async addToGlobalSessionIndex(sessionId: string): Promise<void> {
    try {
      const globalIndexKey = this.getGlobalSessionIndexKey();
      const existingSessions = await this.kwaipilotKV.getObject<string[]>(globalIndexKey, []);

      if (!existingSessions.includes(sessionId)) {
        existingSessions.push(sessionId);
        await this.kwaipilotKV.set(globalIndexKey, existingSessions);
      }
    }
    catch (error) {
      console.error("Error adding session to global index:", error);
    }
  }

  /**
   * 生成全局会话索引键
   * @returns 全局会话索引键
   */
  private getGlobalSessionIndexKey(): string {
    return "blockCodeGlobalSessionIndex";
  }

  /**
   * 生成会话索引键
   * @param sessionId 会话ID
   * @returns 会话索引键
   */
  private getSessionIndexKey(sessionId: string): string {
    return `blockCodeSessionIndex:${sessionId}`;
  }

  /**
   * 获取指定会话的所有缓存数据
   * @param sessionId 会话ID
   * @returns 会话的所有缓存项
   */
  async getSessionCacheItems(sessionId: string): Promise<BlockCodeCacheItem[]> {
    try {
      const indexKey = this.getSessionIndexKey(sessionId);
      const cacheKeys = await this.kwaipilotKV.getObject<string[]>(indexKey, []);

      const cacheItems: BlockCodeCacheItem[] = [];
      const now = Date.now();
      const validKeys: string[] = [];

      for (const cacheKey of cacheKeys) {
        const cacheItem = await this.kwaipilotKV.getObject<BlockCodeCacheItem>(cacheKey);

        if (cacheItem) {
          // 检查是否过期
          if (now - cacheItem.timestamp <= this.CACHE_TTL) {
            // 确保缓存项包含缓存键
            if (!cacheItem.cacheKey) {
              cacheItem.cacheKey = cacheKey;
            }
            cacheItems.push(cacheItem);
            validKeys.push(cacheKey);
          }
          else {
            // 过期了，删除缓存
            await this.kwaipilotKV.delete(cacheKey);
          }
        }
      }

      // 更新索引，移除无效的键
      if (validKeys.length !== cacheKeys.length) {
        await this.kwaipilotKV.set(indexKey, validKeys);
      }

      return cacheItems;
    }
    catch (error) {
      console.error("Error getting session cache items:", error);
      return [];
    }
  }

  /**
   * 清除指定会话的所有缓存
   * @param sessionId 会话ID
   */
  async clearSessionCache(sessionId: string): Promise<void> {
    try {
      const indexKey = this.getSessionIndexKey(sessionId);
      const cacheKeys = await this.kwaipilotKV.getObject<string[]>(indexKey, []);

      // 删除所有缓存项
      for (const cacheKey of cacheKeys) {
        await this.kwaipilotKV.delete(cacheKey);
      }

      // 删除会话索引
      await this.kwaipilotKV.delete(indexKey);

      // 从全局索引中移除会话
      await this.removeFromGlobalSessionIndex(sessionId);
    }
    catch (error) {
      console.error("Error clearing session cache:", error);
    }
  }

  /**
   * 从全局索引中移除会话
   * @param sessionId 会话ID
   */
  private async removeFromGlobalSessionIndex(sessionId: string): Promise<void> {
    try {
      const globalIndexKey = this.getGlobalSessionIndexKey();
      const existingSessions = await this.kwaipilotKV.getObject<string[]>(globalIndexKey, []);

      const updatedSessions = existingSessions.filter(id => id !== sessionId);
      if (updatedSessions.length !== existingSessions.length) {
        await this.kwaipilotKV.set(globalIndexKey, updatedSessions);
      }
    }
    catch (error) {
      console.error("Error removing session from global index:", error);
    }
  }

  /**
   * 清除所有过期的缓存
   */
  async clearExpiredCache(): Promise<void> {
    // 由于使用 hash 键，无法直接遍历所有缓存项
    // 过期的缓存会在 getCachedResult 时被自动清除
    // 这里可以考虑定期清理，但需要额外的索引机制
  }

  /**
   * 清除所有缓存
   */
  async clearAllCache(): Promise<void> {
    try {
      // 1. 从全局索引获取所有会话
      const globalIndexKey = this.getGlobalSessionIndexKey();
      const allSessions = await this.kwaipilotKV.getObject<string[]>(globalIndexKey, []);

      // 2. 清除每个会话的缓存
      for (const sessionId of allSessions) {
        const indexKey = this.getSessionIndexKey(sessionId);
        const cacheKeys = await this.kwaipilotKV.getObject<string[]>(indexKey, []);

        // 删除所有缓存项
        for (const cacheKey of cacheKeys) {
          await this.kwaipilotKV.delete(cacheKey);
        }

        // 删除会话索引
        await this.kwaipilotKV.delete(indexKey);
      }

      // 3. 清除全局索引
      await this.kwaipilotKV.delete(globalIndexKey);

      console.log(`Cleared BlockCode cache for ${allSessions.length} sessions`);
    }
    catch (error) {
      console.error("Error clearing all cache:", error);
    }
  }
}
