import { ServiceModule } from "..";
import { ContextManager } from "../../base/context-manager";
import { SqlLite } from "../sql-lite";
import { MemoryCachedKwaipilotKV } from "../sql-lite/kwaipilotKVCache";
import { LoggerManager } from "../../base/logger";
import { OutPutBlockCodePath, Position } from "shared/lib/misc/blockcode";
import crypto from "crypto";

/**
 * 缓存项的数据结构
 */
export interface BlockCodeCacheItem {
  content: string;
  sessionId: string;
  pos?: Position;
  result: OutPutBlockCodePath | undefined;
  timestamp: number;
  workspaceUri?: string;
}

/**
 * 缓存键的参数
 */
export interface BlockCodeCacheKey {
  content: string;
  sessionId: string;
  pos?: Position;
  workspaceUri?: string;
}

/**
 * BlockCode 结果缓存存储服务
 * 用于持久化存储 getFilePathOfBlockCode 函数的结果，提高性能
 */
export class BlockCodeCacheStorageService extends ServiceModule {
  kwaipilotKV: MemoryCachedKwaipilotKV;

  // 缓存过期时间：24小时
  private readonly CACHE_TTL = 24 * 60 * 60 * 1000;

  constructor(ext: ContextManager) {
    super(ext);
    const sqlLiteService = this.getService(SqlLite);
    this.kwaipilotKV = new MemoryCachedKwaipilotKV(sqlLiteService, this.getBase(LoggerManager));
  }

  /**
   * 生成缓存键
   * @param params 缓存键参数
   * @returns 缓存键字符串
   */
  private generateCacheKey(params: BlockCodeCacheKey): string {
    const { content, sessionId, pos, workspaceUri } = params;

    // 创建一个包含所有参数的字符串
    const keyData = {
      content: content.trim(),
      sessionId,
      pos: pos
        ? {
            start: { line: pos.start.line, column: pos.start.column, offset: pos.start.offset },
            end: { line: pos.end.line, column: pos.end.column, offset: pos.end.offset },
          }
        : undefined,
      workspaceUri,
    };

    // 使用 JSON.stringify 确保一致性，然后生成 hash
    const keyString = JSON.stringify(keyData);
    const hash = crypto.createHash("md5").update(keyString).digest("hex");

    return `blockCodeCache:${hash}`;
  }

  /**
   * 获取缓存的结果
   * @param params 缓存键参数
   * @returns 缓存的结果，如果不存在或已过期则返回 undefined
   */
  async getCachedResult(params: BlockCodeCacheKey): Promise<OutPutBlockCodePath | undefined> {
    try {
      const cacheKey = this.generateCacheKey(params);
      const cachedItem = await this.kwaipilotKV.getObject<BlockCodeCacheItem>(cacheKey);

      if (!cachedItem) {
        return undefined;
      }

      // 检查是否过期
      const now = Date.now();
      if (now - cachedItem.timestamp > this.CACHE_TTL) {
        // 过期了，删除缓存
        await this.kwaipilotKV.delete(cacheKey);
        return undefined;
      }

      // 验证缓存项的有效性（确保参数匹配）
      if (cachedItem.content !== params.content.trim()
        || cachedItem.sessionId !== params.sessionId
        || cachedItem.workspaceUri !== params.workspaceUri) {
        return undefined;
      }

      return cachedItem.result;
    }
    catch (error) {
      console.error("Error getting cached block code result:", error);
      return undefined;
    }
  }

  /**
   * 缓存结果
   * @param params 缓存键参数
   * @param result 要缓存的结果
   */
  async setCachedResult(params: BlockCodeCacheKey, result: OutPutBlockCodePath | undefined): Promise<void> {
    try {
      const cacheKey = this.generateCacheKey(params);
      const cacheItem: BlockCodeCacheItem = {
        content: params.content.trim(),
        sessionId: params.sessionId,
        pos: params.pos,
        result,
        timestamp: Date.now(),
        workspaceUri: params.workspaceUri,
      };

      await this.kwaipilotKV.set(cacheKey, cacheItem);
    }
    catch (error) {
      console.error("Error setting cached block code result:", error);
    }
  }

  /**
   * 清除指定会话的所有缓存
   * @param sessionId 会话ID
   */
  async clearSessionCache(_sessionId: string): Promise<void> {
    // 由于我们使用 hash 作为键，无法直接按 sessionId 查询
    // 这里可以考虑维护一个 sessionId -> cacheKeys 的映射
    // 或者在需要时清除所有缓存
    // 暂时不实现，因为缓存有 TTL 会自动过期
  }

  /**
   * 清除所有过期的缓存
   */
  async clearExpiredCache(): Promise<void> {
    // 由于使用 hash 键，无法直接遍历所有缓存项
    // 过期的缓存会在 getCachedResult 时被自动清除
    // 这里可以考虑定期清理，但需要额外的索引机制
  }

  /**
   * 清除所有缓存
   */
  async clearAllCache(): Promise<void> {
    // 由于使用 hash 键，这里只能通过前缀来清除
    // 但 MemoryCachedKwaipilotKV 没有提供按前缀删除的方法
    // 可以考虑在未来扩展 KV 接口来支持这个功能
  }
}
