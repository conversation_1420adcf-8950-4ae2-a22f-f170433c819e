import { FilePersistedStateType, InternalLocalMessage_Human, InternalLocalMessage_Tool_EditFile } from "shared/lib/agent/types";
import { ServiceModule } from "..";
import { ContextManager } from "../../base/context-manager";
import { SqlLite } from "../sql-lite";
import { ComposerState, LocalMessage } from "shared/lib/agent";
import { isToolEditFileMessage } from "shared/lib/agent/isToolMessage";
import { MemoryCachedKwaipilotKV } from "../sql-lite/kwaipilotKVCache";
import { LoggerManager } from "../../base/logger";

export type PersistedLocalMessage = InternalLocalMessage_Human
  | Omit<InternalLocalMessage_Tool_EditFile, "workingsetEffect"> & {
    workingsetEffect: Omit<InternalLocalMessage_Tool_EditFile["workingSetEffect"], "status"> & {
      // 不必存储中间状态
      status: FilePersistedStateType;
    };
  }
  | Omit<LocalMessage, "role"> & {
    role: undefined;
  };

export interface PersistedComposerSessionData extends Pick<ComposerState,
  | "currentMessageTs"
  | "editingMessageTs"
  | "sessionId"
  | "workspaceUri"
> {
  localMessages: PersistedLocalMessage[];
  indexed?: boolean;
}

export function isPersistedToolEditFileMessage(message: PersistedLocalMessage): message is InternalLocalMessage_Tool_EditFile {
  return isToolEditFileMessage(message);
}

export class ComposerSessionStorageService extends ServiceModule {
  kwaipilotKV: MemoryCachedKwaipilotKV;
  constructor(ext: ContextManager) {
    super(ext);
    const sqlLiteService = this.getService(SqlLite);
    this.kwaipilotKV = new MemoryCachedKwaipilotKV(sqlLiteService, this.getBase(LoggerManager));
  }

  async getComposerSessionData(sessionId: string): Promise<PersistedComposerSessionData | undefined> {
    const storageKey = this.localMessagePersistedStorageKey(sessionId);
    return this.kwaipilotKV.getObject<PersistedComposerSessionData>(storageKey);
  }

  async setComposerSessionData(sessionId: string, data: PersistedComposerSessionData) {
    const storageKey = this.localMessagePersistedStorageKey(sessionId);
    this.kwaipilotKV.set(storageKey, data);
  }

  private localMessagePersistedStorageKey(sessionKey: string) {
    return `composerData:${sessionKey}`;
  }

  async deleteSession(sessionId: string) {
    const storageKey = this.localMessagePersistedStorageKey(sessionId);
    await this.kwaipilotKV.delete(storageKey);
  }
}
