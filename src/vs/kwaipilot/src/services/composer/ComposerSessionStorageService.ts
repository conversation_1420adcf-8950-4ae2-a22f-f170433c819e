import { FilePersistedStateType, InternalLocalMessage_Human, InternalLocalMessage_Tool_EditFile } from "shared/lib/agent/types";
import { ServiceModule } from "..";
import { ContextManager } from "../../base/context-manager";
import { SqlLite } from "../sql-lite";
import { ComposerState, LocalMessage } from "shared/lib/agent";
import { isToolEditFileMessage } from "shared/lib/agent/isToolMessage";
import { MemoryCachedKwaipilotKV } from "../sql-lite/kwaipilotKVCache";
import { LoggerManager } from "../../base/logger";
import { OutPutBlockCodePath } from "shared/lib/misc/blockcode";

export type PersistedLocalMessage = InternalLocalMessage_Human
  | Omit<InternalLocalMessage_Tool_EditFile, "workingsetEffect"> & {
    workingsetEffect: Omit<InternalLocalMessage_Tool_EditFile["workingSetEffect"], "status"> & {
      // 不必存储中间状态
      status: FilePersistedStateType;
    };
  }
  | Omit<LocalMessage, "role"> & {
    role: undefined;
  };

// 会话缓存路径信息类型
export type SessionCachePathInfo = OutPutBlockCodePath & {
  order: number;
  cacheKey?: string;
};

export interface PersistedComposerSessionData extends Pick<ComposerState,
  | "currentMessageTs"
  | "editingMessageTs"
  | "sessionId"
  | "workspaceUri"
> {
  localMessages: PersistedLocalMessage[];
  indexed?: boolean;
  // 添加缓存路径信息，在会话加载时预加载
  cachePathInfos?: SessionCachePathInfo[];
}

export function isPersistedToolEditFileMessage(message: PersistedLocalMessage): message is InternalLocalMessage_Tool_EditFile {
  return isToolEditFileMessage(message);
}

export class ComposerSessionStorageService extends ServiceModule {
  kwaipilotKV: MemoryCachedKwaipilotKV;
  constructor(ext: ContextManager) {
    super(ext);
    const sqlLiteService = this.getService(SqlLite);
    this.kwaipilotKV = new MemoryCachedKwaipilotKV(sqlLiteService, this.getBase(LoggerManager));
  }

  async getComposerSessionData(sessionId: string): Promise<PersistedComposerSessionData | undefined> {
    const storageKey = this.localMessagePersistedStorageKey(sessionId);
    return this.kwaipilotKV.getObject<PersistedComposerSessionData>(storageKey);
  }

  /**
   * 获取包含缓存路径信息的会话数据
   * @param sessionId 会话ID
   * @returns 包含缓存路径信息的会话数据
   */
  async getComposerSessionDataWithCache(sessionId: string): Promise<PersistedComposerSessionData | undefined> {
    const sessionData = await this.getComposerSessionData(sessionId);
    if (!sessionData) {
      return undefined;
    }

    // 如果已经有缓存路径信息，直接返回
    if (sessionData.cachePathInfos) {
      return sessionData;
    }

    // 获取缓存路径信息
    try {
      const { BlockCodeService } = await import("./BlockCodeService");
      const blockCodeService = this.getService(BlockCodeService);
      const cachePathInfos = await blockCodeService.getAllSessionCachePathInfo({ sessionId });

      // 更新会话数据，包含缓存路径信息
      const updatedSessionData: PersistedComposerSessionData = {
        ...sessionData,
        cachePathInfos: cachePathInfos as SessionCachePathInfo[],
      };

      // 异步保存更新后的数据，不阻塞返回
      this.setComposerSessionData(sessionId, updatedSessionData).catch(error =>
        console.warn(`Failed to update session data with cache info:`, error),
      );

      return updatedSessionData;
    }
    catch (error) {
      console.warn(`Failed to load cache path info for session ${sessionId}:`, error);
      return sessionData;
    }
  }

  async setComposerSessionData(sessionId: string, data: PersistedComposerSessionData) {
    const storageKey = this.localMessagePersistedStorageKey(sessionId);
    this.kwaipilotKV.set(storageKey, data);
  }

  private localMessagePersistedStorageKey(sessionKey: string) {
    return `composerData:${sessionKey}`;
  }

  async deleteSession(sessionId: string) {
    const storageKey = this.localMessagePersistedStorageKey(sessionId);
    await this.kwaipilotKV.delete(storageKey);
  }
}
