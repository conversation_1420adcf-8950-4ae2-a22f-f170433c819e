import * as vscode from "vscode";
import { ServiceModule } from "..";
import * as path from "path";
import { ComposerSessionStorageService } from "./ComposerSessionStorageService";
import { InputBlockCodePath, OutPutBlockCodePath, Position } from "shared/lib/misc/blockcode";
import { MentionNodeV2Structure } from "shared/lib/MentionNodeV2/nodes";
import { SymbolKind } from "vscode";
import { BlockCodeCacheStorageService } from "./BlockCodeCacheStorageService";

type PosPathInfo = OutPutBlockCodePath & {
  order: number;
  cacheKey?: string; // 添加缓存键字段
};

export class BlockCodeService extends ServiceModule {
  async getAllSessionCachePathInfo(data: { sessionId: string }): Promise<PosPathInfo[]> {
    const { sessionId } = data;
    const blockCodeCacheService = this.getService(BlockCodeCacheStorageService);

    try {
      // 获取当前 sessionId 的所有缓存项
      const cacheItems = await blockCodeCacheService.getSessionCacheItems(sessionId);

      // 转换为 PosPathInfo 格式，只返回有结果的项
      const pathInfos: PosPathInfo[] = [];

      for (const cacheItem of cacheItems) {
        if (cacheItem.result) {
          // 生成前端缓存键（与前端逻辑保持一致）
          const frontendCacheKey = this.generateFrontendCacheKey(
            cacheItem.content,
            cacheItem.sessionId,
            cacheItem.pos,
          );

          // 将缓存结果转换为 PosPathInfo 格式
          const pathInfo: PosPathInfo = {
            ...cacheItem.result,
            order: 100, // 缓存的结果给予高优先级
            cacheKey: frontendCacheKey, // 添加前端缓存键
          };
          pathInfos.push(pathInfo);
        }
      }

      // 按文件路径分组，去重
      const uniquePathInfos = this.deduplicatePathInfos(pathInfos);

      // 按优先级排序
      uniquePathInfos.sort((a, b) => (b.order || 0) - (a.order || 0));

      return uniquePathInfos;
    }
    catch (error) {
      console.error("Error getting session cache path info:", error);
      return [];
    }
  }

  /**
   * 生成前端缓存键（与前端逻辑保持一致）
   * @param content 内容
   * @param sessionId 会话ID
   * @param pos 位置信息
   * @returns 前端缓存键
   */
  private generateFrontendCacheKey(content: string, sessionId: string, pos?: Position): string {
    const posKey = pos ? `${pos.start.line}-${pos.start.column}-${pos.end.line}-${pos.end.column}` : "";
    return `${content}:${sessionId}:${posKey}`;
  }

  /**
   * 获取会话中所有唯一的文件路径
   * @param data 包含 sessionId 的数据
   * @returns 唯一文件路径数组
   */
  async getSessionUniqueFilePaths(data: { sessionId: string }): Promise<string[]> {
    const pathInfos = await this.getAllSessionCachePathInfo(data);
    const uniquePaths = new Set<string>();

    for (const pathInfo of pathInfos) {
      if (pathInfo.filepath) {
        uniquePaths.add(pathInfo.filepath);
      }
    }

    return Array.from(uniquePaths);
  }

  /**
   * 获取会话中特定文件的所有代码位置
   * @param data 包含 sessionId 和 filepath 的数据
   * @returns 该文件的所有代码位置
   */
  async getSessionFileCodePositions(data: { sessionId: string; filepath: string }): Promise<PosPathInfo[]> {
    const { filepath } = data;
    const pathInfos = await this.getAllSessionCachePathInfo(data);

    return pathInfos.filter(pathInfo => pathInfo.filepath === filepath);
  }

  /**
   * 去重路径信息，相同文件路径的保留优先级最高的
   * @param pathInfos 路径信息数组
   * @returns 去重后的路径信息数组
   */
  private deduplicatePathInfos(pathInfos: PosPathInfo[]): PosPathInfo[] {
    const pathMap = new Map<string, PosPathInfo>();

    for (const pathInfo of pathInfos) {
      const key = `${pathInfo.filepath}:${pathInfo.startLine}:${pathInfo.endLine}`;
      const existing = pathMap.get(key);

      if (!existing || (pathInfo.order || 0) > (existing.order || 0)) {
        pathMap.set(key, pathInfo);
      }
    }

    return Array.from(pathMap.values());
  }

  async getFilePathOfBlockCode(data: { content: string; sessionId: string; pos?: Position }): Promise<OutPutBlockCodePath | undefined> {
    const { content, sessionId, pos } = data;

    // 获取当前工作区 URI
    const workspaceUri = this.context.storageUri?.toString();

    // 1. 先检查缓存
    const cacheKey = {
      content,
      sessionId,
      pos,
      workspaceUri,
    };

    const blockCodeCacheService = this.getService(BlockCodeCacheStorageService);
    const cachedResult = await blockCodeCacheService.getCachedResult(cacheKey);
    if (cachedResult !== undefined) {
      return cachedResult;
    }

    // 2. 缓存未命中，执行原有逻辑
    // 过滤掉 注释代码  比如  // # -  ，单独用一个函数来实现
    const filteredContent = this.filterComments(content.trim());

    let result: OutPutBlockCodePath | undefined;

    // 3. 区分是 文件路径  还是 代码块
    if (this.isFilePath(filteredContent)) {
      result = await this.getFilePathByContent(filteredContent, sessionId);
    }
    else {
      result = await this.getCodePathByContent(filteredContent, sessionId, pos);
    }

    // 4. 异步 结果存入缓存
    blockCodeCacheService.setCachedResult(cacheKey, result);

    return result;
  }

  /**
   * 根据内容获取文件路径
   * @param content 内容
   * @returns 文件路径字符串
   */
  async getFilePathByContent(content: string, sessionId: string): Promise<OutPutBlockCodePath | undefined> {
    // 处理文件路径格式，如果有行号信息则提取
    const lineNumberMatch = content.match(/:(\d+)(-(\d+))?$/);
    const filepath = content.substring(0, content.lastIndexOf(":")) || content;
    let startLine: number | undefined;
    let endLine: number | undefined;
    if (lineNumberMatch) {
      startLine = parseInt(lineNumberMatch[1], 10);
      endLine = lineNumberMatch[3] ? parseInt(lineNumberMatch[3], 10) : startLine;
    }

    // 判断路径信息是否合理
    const { isFileExist, isLineExist } = await this.isPathValid({
      filepath,
      startLine,
      endLine,
    });

    // 如果都存在
    if (isFileExist && isLineExist) {
      return {
        filepath,
        startLine,
        endLine,
        type: "path",
        symbolKind: SymbolKind.File,
      };
    }

    // 如果文件存在，行号不存在，则返回文件路径
    if (isFileExist && !isLineExist) {
      return {
        filepath,
        type: "path",
        symbolKind: SymbolKind.File,
      };
    }

    // 如果文件不存在，也可能是路径不全
    if (!isFileExist) {
      // 这里从历史记录里去匹配下  是否提到过这个路径
      const paths = (await this.extractPathInfoFromHistory(content, sessionId)) || [];
      // 从里面模糊匹配下可能的路径
      const suggestPaths = paths.filter(p => p.includes(filepath));
      if (suggestPaths.length) {
        // 取最近的一个，倒数
        const lastItem = suggestPaths[suggestPaths.length - 1];
        return {
          symbolKind: SymbolKind.File,
          ...this.convertToBlockPath(lastItem),
        };
      }
      // 否则采取其他策略 todo
    }
  }

  /**
   * 根据内容获取代码路径
   * @param content 内容
   * @returns 代码路径对象
   */
  async getCodePathByContent(content: string, sessionId: string, pos?: Position): Promise<OutPutBlockCodePath | undefined> {
    // 获取历史会话信息，从里面提前文件路径信息
    const paths = (await this.extractPathInfoFromHistory(content, sessionId, pos)) || [];
    const validPaths = [];
    // 过滤掉不合理的路径信息
    for (const p of paths) {
      const p2 = await this.getCodePosFromPath(p);
      if (!p2) {
        continue;
      }
      const { isFileExist } = await this.isPathValid(p2);
      if (isFileExist) {
        validPaths.push(p);
      }
    }
    if (!validPaths.length) {
      return;
    }

    // 从这些路径去 找 content 到底在 哪个位置
    const res = (await Promise.all(validPaths.map(p => this.getCodePosFromPath(p, content)))).filter(item => item?.startLine || item?.endLine).filter(Boolean) as (PosPathInfo)[];
    if (!res.length) {
      return;
    }
    // 这里按 order 进行取值 order 越大越准确
    res.sort((a, b) => (b.order || 0) - (a.order || 0));
    return res[0];
  }

  /**
   * 过滤掉注释代码
   * @param content 原始内容
   * @returns 过滤后的内容
   */
  private filterComments(content: string): string {
    // 按行分割
    const lines = content.split("\n");
    // 过滤掉以 // # - 开头的注释行
    const filteredLines = lines.filter(line => !line.trim().startsWith("// #") && !line.trim().startsWith("// -"));
    return filteredLines.join("\n");
  }

  /**
   * 判断内容是否为文件路径
   * @param content 待检查内容
   * @returns 是否为文件路径
   */
  private isFilePath(content: string): boolean {
    // 简单判断文件路径的规则：
    // 1. 包含文件扩展名
    // 2. 不包含多行代码
    // 3. 可能包含常见的路径分隔符
    const lines = content.trim().split("\n");
    if (lines.length > 1) {
      return false;
    }

    const pathPattern = /^(\.{0,2}\/)?[\w\d\-_/.]+\.\w+(:\d+(-\d+)?)?$/;
    return pathPattern.test(content.trim());
  }

  private async isPathValid(info: InputBlockCodePath): Promise<{ isFileExist: boolean; isLineExist: boolean }> {
    const { filepath, startLine, endLine } = info;

    let isFileExist = false;
    let isLineExist = false;

    // filepath 可能是相对路径
    try {
      // Convert relative path to absolute path if needed
      const workspacePath = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
      const absolutePath = filepath.startsWith("/") || filepath.match(/^[A-Z]:\\/i)
        ? filepath // Already absolute path
        : workspacePath
          ? path.join(workspacePath, filepath)
          : filepath;

      // Use vscode API to check if file exists
      const uri = vscode.Uri.file(absolutePath);
      try {
        await vscode.workspace.fs.stat(uri);
        isFileExist = true;

        // If file exists and line numbers are specified, check if they're valid
        if (isFileExist && (startLine !== undefined || endLine !== undefined)) {
          const document = await vscode.workspace.openTextDocument(uri);
          const lineCount = document.lineCount;

          // Check if line numbers are within valid range
          isLineExist
            = (startLine === undefined || (startLine > 0 && startLine <= lineCount))
            && (endLine === undefined || (endLine > 0 && endLine <= lineCount));
        }
      }
      catch (error) {
        // File doesn't exist
        isFileExist = false;
      }
    }
    catch (error) {
      console.error("Error checking path validity:", error);
    }

    return {
      isFileExist,
      isLineExist: startLine === undefined ? true : isLineExist,
    };
  }

  private async extractPathInfoFromHistory(content: string, sessionId: string, pos?: Position): Promise<string[] | undefined> {
    // 从历史会话中提取路径信息
    const composerSessionStorageService = this.getService(ComposerSessionStorageService);
    const session = await composerSessionStorageService.getComposerSessionData(sessionId);
    if (!session) return;
    const historys = session.localMessages;
    // 需要缩小搜索范围，从 content 在history 第一次出现为止
    const targetIndex = historys.findIndex((v) => {
      if (!v.text) return false;
      // 按照 pos 去找对应的 history item
      if (pos) {
        const targetText = v.text.slice(pos.start.offset, pos.end.offset);
        return targetText.includes(content);
      }
      return v.text.includes(content);
    });
    if (targetIndex === -1) return;
    const list = historys.slice(0, targetIndex + 1);
    // 用户引用的路径信息
    const userSelectPaths = list.filter(v => v.role === "user" && v.contextItems.length).filter(Boolean).map(v => v.role === "user" ? v?.contextItems : []).flat(1).map(p => this.convertToUnionPath(p));
    // 工具调用的路径信息
    const toolsPaths = list.filter(v => v.say === "tool" && v.text).filter(Boolean).map(v => this.extractToolPathInfoFromContent(v?.text || "")).filter(Boolean).flat(1).map(p => this.convertToUnionPath(p));
    // 当前打开的 tab 路径信息
    const activeTabPath = vscode.window.activeTextEditor?.document.uri.fsPath || "";

    return [...new Set([activeTabPath, ...userSelectPaths, ...toolsPaths])].filter(Boolean).map(p => p.replace("file://", ""));
  }

  private extractToolPathInfoFromContent(content: string): string {
    try {
      // {"tool":"readFile","path":"src/extension.ts","startLine":1,"endLine":100,"shouldReadEntireFile":true,"content":"/Users/<USER>/Work/ai-ide-plugins/kwaipilot-vscode-extension/src/extension.ts"}
      const { tool, path } = JSON.parse(content);
      const whilteList = [
        "readFile",
        "grepSearch",
        "codebaseSearch",
      ];
      if (!whilteList.includes(tool)) {
        return "";
      }
      return path;
    }
    catch (error) {
      return "";
    }
  }

  private async getCodePosFromPath(filepath: string, content?: string): Promise< PosPathInfo | undefined> {
    try {
      // 解析 URI，处理各种格式的文件路径和行列号
      let uri: vscode.Uri;
      let startLine: number | undefined;
      let endLine: number | undefined;
      let startCharacter: number | undefined;
      let endCharacter: number | undefined;

      // 处理 URI 格式
      if (filepath.includes("#")) {
        // 分离文件路径和行列号部分
        const [filePart, fragmentPart] = filepath.split("#");
        uri = vscode.Uri.parse(filePart);

        // 处理 fragment 部分 (行列号)
        if (fragmentPart) {
          // 处理带列号的格式 L1,5-L2,10 或 1,5-2,10
          const fullPattern = /L?(\d+),(\d+)(?:-L?(\d+),(\d+))?/;
          const fullMatch = fragmentPart.match(fullPattern);

          if (fullMatch) {
            startLine = parseInt(fullMatch[1], 10);
            startCharacter = parseInt(fullMatch[2], 10);
            endLine = fullMatch[3] ? parseInt(fullMatch[3], 10) : startLine;
            endCharacter = fullMatch[4] ? parseInt(fullMatch[4], 10) : startCharacter;
          }
          else {
            // 只有行号的格式 L1-L2 或 1-2
            const linePattern = /L?(\d+)(?:-L?(\d+))?/;
            const lineMatch = fragmentPart.match(linePattern);

            if (lineMatch) {
              startLine = parseInt(lineMatch[1], 10);
              endLine = lineMatch[2] ? parseInt(lineMatch[2], 10) : startLine;
            }
          }
        }
      }
      else if (filepath.includes(":") && !filepath.startsWith("file:")) {
        // 处理 filepath:line,col-line,col 格式
        const parts = filepath.split(":");
        uri = vscode.Uri.parse(parts[0]);

        if (parts.length > 1) {
          const rangeText = parts[1];

          // 检查是否包含列号 (1,2-3,4 格式)
          const fullPattern = /(\d+),(\d+)(?:-(\d+),(\d+))?/;
          const fullMatch = rangeText.match(fullPattern);

          if (fullMatch) {
            startLine = parseInt(fullMatch[1], 10);
            startCharacter = parseInt(fullMatch[2], 10);
            endLine = fullMatch[3] ? parseInt(fullMatch[3], 10) : startLine;
            endCharacter = fullMatch[4] ? parseInt(fullMatch[4], 10) : startCharacter;
          }
          else {
            // 只有行号 (1-3 格式)
            const linePattern = /(\d+)(?:-(\d+))?/;
            const lineMatch = rangeText.match(linePattern);

            if (lineMatch) {
              startLine = parseInt(lineMatch[1], 10);
              endLine = lineMatch[2] ? parseInt(lineMatch[2], 10) : startLine;
            }
          }
        }
      }
      else {
        // 需要考虑相对路径
        const workspacePath = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        const absolutePath = filepath.startsWith("/") || filepath.match(/^[A-Z]:\\/i)
          ? filepath // Already absolute path
          : workspacePath
            ? path.join(workspacePath, filepath)
            : filepath;
        uri = vscode.Uri.parse(absolutePath);
      }

      // 确保文件路径正确
      let fsPath = uri.fsPath;
      if (uri.scheme === "file") {
        // 已经是绝对路径
        fsPath = uri.fsPath;
      }
      else {
        // 可能是相对路径，需要转换
        const workspacePath = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        if (workspacePath) {
          fsPath = path.isAbsolute(uri.path) ? uri.path : path.join(workspacePath, uri.path);
        }
        else {
          fsPath = uri.path;
        }
      }
      // 构建返回结果
      const result: PosPathInfo = {
        filepath: fsPath,
        type: "path",
        // 给结果添加优先级
        order: 0,
      };
      // 如果提供了内容且没有指定行号，则在文件中查找内容
      if (content && (!startLine || !endLine)) {
        try {
          const documentUri = vscode.Uri.file(fsPath);
          const document = await vscode.workspace.openTextDocument(documentUri);
          const fileContent = document.getText();

          // Get document symbols (outline information)
          const symbols = await vscode.commands.executeCommand<vscode.DocumentSymbol[]>(
            "vscode.executeDocumentSymbolProvider",
            documentUri,
          );

          // 先尝试在outline中查找匹配
          const matchedSymbol = symbols && this.findSymbolByContent(symbols, content, document);

          if (matchedSymbol) {
            // 获取符号的文本内容
            const symbolText = document.getText(matchedSymbol.range);

            // 在符号内容中精确定位目标内容的位置
            const contentTrimmed = content.trim();
            const contentIndex = symbolText.indexOf(contentTrimmed);

            if (matchedSymbol.name === contentTrimmed) {
              result.symbolKind = matchedSymbol.kind;
            }

            if (contentIndex !== -1) {
              // 计算内容在文档中的精确位置
              const symbolStartOffset = document.offsetAt(matchedSymbol.range.start);
              const contentStartOffset = symbolStartOffset + contentIndex;
              const contentEndOffset = contentStartOffset + contentTrimmed.length;

              // 转换为行列号
              const contentStartPos = document.positionAt(contentStartOffset);
              const contentEndPos = document.positionAt(contentEndOffset);

              // 设置精确的行列范围
              startLine = contentStartPos.line + 1; // 转换为基于1的行号
              endLine = contentEndPos.line + 1;
              startCharacter = contentStartPos.character + 1;
              endCharacter = contentEndPos.character + 1;
              result.order = 100;
            }
          }
          else {
            // 如果在outline中没找到，回退到按行搜索策略
            const contentLines = content.trim().split("\n");
            const fileLines = fileContent.split("\n");

            // 搜索内容在文件中的位置，使用精确匹配
            for (let i = 0; i < fileLines.length; i++) {
              // 检查第一行是否精确匹配
              const firstLineContent = contentLines[0].trim();
              const currentLine = fileLines[i].trim();

              // 使用正则表达式进行精确匹配
              const regex = new RegExp(`(^|[^\\w$])${escapeRegExp(firstLineContent)}([^\\w$]|$)`);
              if (regex.test(currentLine) || currentLine === firstLineContent) {
                let matchFound = true;

                // 检查后续行
                for (let j = 1; j < contentLines.length && i + j < fileLines.length; j++) {
                  const nextLineContent = contentLines[j].trim();
                  const nextFileLine = fileLines[i + j].trim();

                  // 同样使用精确匹配
                  const nextLineRegex = new RegExp(`(^|[^\\w$])${escapeRegExp(nextLineContent)}([^\\w$]|$)`);
                  if (!(nextLineRegex.test(nextFileLine) || nextFileLine === nextLineContent)) {
                    matchFound = false;
                    break;
                  }
                }

                if (matchFound) {
                  startLine = i + 1; // 转换为基于1的行号
                  endLine = i + contentLines.length;

                  // 找到第一行的精确字符位置
                  const firstLineMatch = fileLines[i].match(new RegExp(`(^|[^\\w$])(${escapeRegExp(firstLineContent)})([^\\w$]|$)`));
                  startCharacter = firstLineMatch
                    ? fileLines[i].indexOf(firstLineMatch[2]) + 1
                    : fileLines[i].indexOf(firstLineContent) + 1;

                  // 找到最后一行的精确字符位置
                  const lastContentLine = contentLines[contentLines.length - 1].trim();
                  const lastLineMatch = fileLines[i + contentLines.length - 1].match(
                    new RegExp(`(^|[^\\w$])(${escapeRegExp(lastContentLine)})([^\\w$]|$)`),
                  );

                  const lastLineIndex = lastLineMatch
                    ? fileLines[i + contentLines.length - 1].indexOf(lastLineMatch[2])
                    : fileLines[i + contentLines.length - 1].indexOf(lastContentLine);

                  endCharacter = lastLineIndex + lastContentLine.length + 1;
                  result.order = 80;
                  break;
                }
              }
            }
          }
        }
        catch (error) {
          console.error("Error searching content in file:", error);
        }
      }

      if (startLine) result.startLine = startLine;
      if (endLine) result.endLine = endLine;

      // 如果有行号和列号，构建完整的 range
      if (startLine && endLine) {
        // 行号从 1 开始，但 VSCode 的 Position 从 0 开始
        result.range = {
          start: {
            line: startLine - 1,
            character: startCharacter ? startCharacter - 1 : 0,
          },
          end: {
            line: endLine - 1,
            character: endCharacter ? endCharacter - 1 : Number.MAX_SAFE_INTEGER,
          },
        };
        result.type = "code";
      }

      return result;
    }
    catch (error) {
      console.error("Error finding code position:", error);
      return undefined;
    }
  }

  private convertToUnionPath(item: MentionNodeV2Structure | string) {
    try {
      if (typeof item === "string") {
        return item;
      }
      // FileItem
      if (item.type === "file") {
        return item.uri;
      }
      // SelectionItem file:///some/file.js#73,84-83,52
      if (item.type === "selection") {
        const { uri, range } = item;
        const { start, end } = range;
        const startLine = start.line + 1;
        const endLine = end.line + 1;
        const startColumn = start.character + 1;
        const endColumn = end.character + 1;
        // file:///some/file.js#73,84-83,52
        return `${uri}#${startLine},${startColumn}-${endLine},${endColumn}`;
      }
      return "";
    }
    catch (err) {
      return "";
    }
  }

  private convertToBlockPath(path: string): OutPutBlockCodePath {
    // file:///some/file.js#73,84-83,52
    const [uri, part] = path.split("#");
    const [start, end] = part.split("-");
    const [startLine, startColumn] = start.split(",");
    const [endLine, endColumn] = end.split(",");
    let range = undefined;

    if (startLine && endLine) {
      range = {
        start: {
          line: parseInt(startLine) - 1,
          character: parseInt(startColumn) - 1,
        },
        end: {
          line: parseInt(endLine) - 1,
          character: parseInt(endColumn) - 1,

        },
      };
    }

    return {
      filepath: uri,
      startLine: !startLine ? undefined : parseInt(startLine),
      endLine: !endLine ? undefined : parseInt(endLine),
      range,
      type: "path",
    };
  }

  /**
   * 在文档符号(outline)中查找匹配内容的符号
   * @param symbols 文档符号数组
   * @param content 要查找的内容
   * @param document 文档对象
   * @returns 匹配的符号或undefined
   */
  private findSymbolByContent(
    symbols: vscode.DocumentSymbol[] | undefined,
    content: string,
    document: vscode.TextDocument,
  ): vscode.DocumentSymbol | undefined {
    if (!symbols || symbols.length === 0) return undefined;

    const contentTrimmed = content.trim();

    // 递归搜索符号树
    const searchSymbols = (symbolList: vscode.DocumentSymbol[]): vscode.DocumentSymbol | undefined => {
      for (const symbol of symbolList) {
        // 获取符号的文本内容
        const range = symbol.range;
        const symbolText = document.getText(range);

        if (symbol.name === contentTrimmed) {
          return symbol;
        }

        // 检查是否为精确匹配
        // 1. 完全相等
        if (symbolText === contentTrimmed) {
          return symbol;
        }

        // // 2. 检查是否为完整标识符/表达式匹配
        // // 使用正则表达式匹配完整的标识符或表达式
        // const regex = new RegExp(`(^|[^\\w$])${escapeRegExp(contentTrimmed)}([^\\w$]|$)`, "g");
        // if (regex.test(symbolText)) {
        //   return symbol;
        // }

        // 递归检查子符号
        if (symbol.children && symbol.children.length > 0) {
          const found = searchSymbols(symbol.children);
          if (found) return found;
        }
      }
      return undefined;
    };

    return searchSymbols(symbols);
  }
}

// 辅助函数：转义正则表达式中的特殊字符
function escapeRegExp(string: string): string {
  return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
};
