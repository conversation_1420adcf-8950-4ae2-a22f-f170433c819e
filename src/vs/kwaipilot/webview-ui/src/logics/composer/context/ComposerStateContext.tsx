import { kwai<PERSON>ilotBridgeAPI } from "@/bridge";
import { useKwaipilotBridgeMessageListener } from "@/hooks/useKwaipilotBridgeMessageListener";
import { DOM } from "@/utils/dom";
import { WEBVIEW_BRIDGE_EVENT_NAME } from "@shared/types/bridge";
import { createContext, useCallback, useContext, useMemo, useState, useEffect } from "react";
import { Ask, ComposerState, InternalLocalMessage, Say } from "shared/lib/agent";
import { SupportedModelEnum } from "shared/lib/agent/supportedModels";
import { OutPutBlockCodePath } from "shared/lib/misc/blockcode";

export interface ComposerDerivedState {
  isStreaming: boolean;
}

// 会话缓存路径信息类型
export type SessionCachePathInfo = OutPutBlockCodePath & {
  order: number;
  cacheKey?: string; // 添加缓存键用于精确匹配
};

interface ComposerStateContextType extends ComposerState, ComposerDerivedState {
  updateSessionSummary: (summary: string) => void;
  isCurrentWorkspaceSession: boolean;
  messageScrollContainerHeight: number;
  setMessageScrollContainerHeight: (height: number) => void;
  // 会话缓存路径信息相关
  sessionCachePathInfos: SessionCachePathInfo[];
  isLoadingSessionCache: boolean;
  refreshSessionCachePathInfos: () => Promise<void>;
  getPathInfoByContent: (cacheKey: string) => SessionCachePathInfo | undefined;
  clearSessionCachePathInfos: () => void; // 清理会话缓存
}

const EndSayMessage: Say[] = ["completion_result", "error"];

const ComposerStateContext = createContext<ComposerStateContextType | undefined>(undefined);

export function ComposerStateContextProvider({ children }: { children: React.ReactNode }) {
  const [state, setState] = useState<ComposerState>({
    localMessages: [],
    workspaceUri: "",
    sessionId: "",
    currentTaskInterrupted: false,
    indeterminatedWorkingSetEffects: [],
    isCurrentWorkspaceSession: true,
    editingMessageTs: undefined,
    currentMessageTs: undefined,
    userPreferredModel: SupportedModelEnum.claude3,
    localServiceConnectionLost: false,
  });

  const scrollToBottom = useCallback(() => {
    const scrollContainer = DOM.$("composer-v2-message-scroll-container");
    if (scrollContainer) {
      // 定义安全距离（像素）
      const safeDistance = 10;

      // 由于使用了 flex-col-reverse，滚动位置是反向的
      // scrollTop 为 0 表示在底部，scrollHeight - clientHeight - scrollTop 为 0 表示在顶部
      // 当前距离底部的距离
      const distanceFromBottom = scrollContainer.scrollTop;

      // 如果距离底部的距离小于安全距离，则滚动到底部
      if (Math.abs(distanceFromBottom) <= safeDistance) {
        scrollContainer.scrollTop = 0;
      }
    }
  }, []);

  const onComposerStateUpdate = useCallback((newState: ComposerState) => {
    setState(newState);
  }, []);
  useKwaipilotBridgeMessageListener(WEBVIEW_BRIDGE_EVENT_NAME.COMPOSER_STATE_UPDATE, onComposerStateUpdate);

  const onComposerPartialMessage = useCallback(({ partialMessage }: { partialMessage: InternalLocalMessage }) => {
    setState((prevState) => {
      // worth noting it will never be possible for a more up-to-date message to be sent here or in normal messages post since the presentAssistantContent function uses lock
      const lastIndex = prevState.localMessages.findLastIndex(msg => msg.ts === partialMessage.ts);
      if (lastIndex !== -1) {
        const newLocalMessages = [...prevState.localMessages];
        newLocalMessages[lastIndex] = partialMessage;
        return { ...prevState, localMessages: newLocalMessages };
      }
      return prevState;
    });
    scrollToBottom();
  }, [scrollToBottom]);
  useKwaipilotBridgeMessageListener(WEBVIEW_BRIDGE_EVENT_NAME.COMPOSER_PARTIAL_MESSAGE, onComposerPartialMessage);

  const lastMessage = useMemo(() => state.localMessages.at(-1), [state.localMessages]);

  const currentAsking = useMemo<Ask | undefined>(() => {
    if (!lastMessage) {
      return undefined;
    }
    if (lastMessage.type === "say") {
      return undefined;
    }
    return lastMessage.ask;
  }, [lastMessage]);

  const isStreaming = useMemo(() => {
    const lastMessage = state.localMessages.at(-1);

    if (currentAsking && !lastMessage?.partial) {
      return false;
    }
    if (state.currentTaskInterrupted) {
      return false;
    }

    if (lastMessage?.type === "say" && lastMessage.say && !EndSayMessage.includes(lastMessage.say)) {
      // 最后一条是 api_req_start 表示一定没有完成
      return true;
    }
    const isLastMessagePartial = lastMessage?.partial === true;
    if (isLastMessagePartial) {
      return true;
    }

    return false;
  }, [currentAsking, state.currentTaskInterrupted, state.localMessages]);

  const [messageScrollContainerHeight, setMessageScrollContainerHeight] = useState(0);

  // 会话缓存路径信息状态
  const [sessionCachePathInfos, setSessionCachePathInfos] = useState<SessionCachePathInfo[]>([]);
  const [isLoadingSessionCache, setIsLoadingSessionCache] = useState(false);

  // 刷新会话缓存路径信息
  const refreshSessionCachePathInfos = useCallback(async () => {
    if (!state.sessionId) return;

    setIsLoadingSessionCache(true);
    try {
      const pathInfos = await kwaiPilotBridgeAPI.editor.getSessionCachePathInfo(state.sessionId);
      setSessionCachePathInfos(pathInfos);
    }
    catch (error) {
      console.error("Failed to load session cache path infos:", error);
      setSessionCachePathInfos([]);
    }
    finally {
      setIsLoadingSessionCache(false);
    }
  }, [state.sessionId]);

  // 根据缓存键获取路径信息
  const getPathInfoByContent = useCallback((cacheKey: string): SessionCachePathInfo | undefined => {
    return sessionCachePathInfos.find((pathInfo) => {
      // 优先使用精确的缓存键匹配
      if (pathInfo.cacheKey === cacheKey) {
        return true;
      }

      return false;
    });
  }, [sessionCachePathInfos]);

  // 清理会话缓存路径信息
  const clearSessionCachePathInfos = useCallback(() => {
    setSessionCachePathInfos([]);
    setIsLoadingSessionCache(false);
  }, []);

  // 当 sessionId 变化时，自动刷新缓存路径信息
  useEffect(() => {
    if (state.sessionId) {
      refreshSessionCachePathInfos();
    }
    else {
      setSessionCachePathInfos([]);
    }
  }, [state.sessionId, refreshSessionCachePathInfos]);

  const contextValue: ComposerStateContextType = {
    ...state,
    isStreaming,
    updateSessionSummary: (summary: string) => {
      kwaiPilotBridgeAPI.updateComposerSessionName({
        sessionId: state.sessionId,
        name: summary,
      });
    },
    messageScrollContainerHeight,
    setMessageScrollContainerHeight,
    // 会话缓存路径信息相关
    sessionCachePathInfos,
    isLoadingSessionCache,
    refreshSessionCachePathInfos,
    getPathInfoByContent,
    clearSessionCachePathInfos,
  };

  return (
    <ComposerStateContext.Provider value={contextValue}>
      {children}
    </ComposerStateContext.Provider>
  );
}

export const useComposerState = () => {
  const context = useContext(ComposerStateContext);
  if (context === undefined) {
    throw new Error("useComposerState must be used within an ComposerStateContextProvider");
  }
  return context;
};
